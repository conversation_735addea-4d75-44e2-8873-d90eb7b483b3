import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

interface Event {
  id: string;
  company: {
    name: string;
    logo: string;
    industry: string;
  };
  type: 'EVENT';
  title: string;
  description: string;
  tags: string[];
  postedAt: string;
  eventDate: string;
  location: string;
  isNew?: boolean;
  isOnline?: boolean;
  bannerImage?: string;
  attendees?: number;
}

interface FilterItem {
  label: string;
  checked: boolean;
}

@Component({
  selector: 'app-events',
  templateUrl: './events.component.html',
  styleUrls: ['./events.component.css'],
})
export class EventsComponent implements OnInit {
  events: Event[] = [];
  selectedFilter: string = 'All';
  searchQuery: string = '';
  isMobileFiltersOpen: boolean = false;

  filters = ['All', 'Featured', 'Virtual', 'In-Person', 'Saved', 'Registered'];

  filterTypes: FilterItem[] = [
    { label: 'Online', checked: false },
    { label: 'In-person', checked: true },
  ];

  industries: FilterItem[] = [
    { label: 'Tech', checked: false },
    { label: 'Finance', checked: false },
    { label: 'Health', checked: false },
    { label: 'Lifestyle', checked: false },
    { label: 'Education', checked: false },
  ];

  dateFilters = [
    { label: 'This Week', value: 'week', checked: false },
    { label: 'This Month', value: 'month', checked: true },
    { label: 'Next 3 Months', value: '3months', checked: false },
    { label: 'All Upcoming', value: 'all', checked: false },
  ];

  constructor(private router: Router) {
    console.log('Events component constructed');
  }

  ngOnInit() {
    console.log('Events component initializing...');
    // Mock data
    this.events = [
      {
        id: '1',
        company: {
          name: 'Eco Ventures',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Environment',
        },
        type: 'EVENT',
        title: 'European MGA Summit 2025',
        description:
          'Join industry leaders for a two-day summit exploring the latest trends in sustainable insurance and environmental risk management.',
        tags: ['SUSTAINABILITY', 'PACKAGING', 'GREENNEWS'],
        postedAt: '2 days ago',
        eventDate: 'Tue, Jun 10, 2025, 1:30 PM',
        location: 'Leonardo Royal Hotel Amsterdam',
        isNew: true,
        isOnline: true,
        bannerImage: 'assets/images/eventsimg.png',
        attendees: 72,
      },
      {
        id: '2',
        company: {
          name: 'Healthcare Connect',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Healthcare',
        },
        type: 'EVENT',
        title: 'Digital Health Transformation Forum',
        description:
          'A virtual conference focusing on the intersection of healthcare and technology...',
        tags: ['HEALTH', 'DIGITAL', 'VIRTUAL'],
        postedAt: '3 days ago',
        eventDate: 'May 20, 2024',
        location: 'Virtual Event',
        isNew: true,
        isOnline: true,
        bannerImage: 'assets/images/eventsimg.png',
        attendees: 45,
      },
    ];
    console.log('Events data loaded:', this.events);
  }

  filterEvents() {
    console.log('Filtering events...');
    // Implement filter logic
  }

  refreshEvents() {
    console.log('Refreshing events...');
    // Implement refresh logic
  }

  registerForEvent(eventId: string) {
    console.log('Registering for event:', eventId);
    // Implement registration logic
  }

  saveEvent(eventId: string) {
    console.log('Saving event:', eventId);
    // Implement save logic
  }

  viewEventDetails(eventId: string) {
    console.log('Navigating to event details:', eventId);
    this.router.navigate(['/dashboard/events', eventId]);
  }

  clearFilters() {
    console.log('Clearing all filters...');
    // Reset filter types
    this.filterTypes.forEach((type) => (type.checked = false));

    // Reset industries
    this.industries.forEach((industry) => (industry.checked = false));

    // Reset date filters - set default to "This Month"
    this.dateFilters.forEach((date) => {
      date.checked = date.value === 'month';
    });

    // Reset selected filter to "All"
    this.selectedFilter = 'All';

    // Reset search query
    this.searchQuery = '';

    // Apply the filter changes
    this.filterEvents();
  }
}
