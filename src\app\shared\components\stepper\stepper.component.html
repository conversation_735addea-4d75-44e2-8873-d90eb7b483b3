<div class="flex items-center justify-center">
  <div class="flex items-start">
    <ng-container *ngFor="let step of steps; let i = index">
      <div class="flex flex-col items-center">
        <div
          class="rounded-full h-10 w-10 flex items-center justify-center text-white"
          [ngClass]="{
            'bg-gradient-primary': i + 1 === currentStep,
            'bg-green-500': i + 1 < currentStep,
            'border-2': i + 1 > currentStep
          }"
        >
          <span *ngIf="i + 1 > currentStep">{{ i + 1 }}</span>
          <svg
            *ngIf="i + 1 <= currentStep"
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <p class="text-sm mt-2">{{ step.title }}</p>
      </div>
      <div
        *ngIf="i < steps.length - 1"
        class="flex-auto border-t-2 transition duration-500 ease-in-out mx-4 mt-5"
        [ngClass]="{
          'border-green-500': i + 1 < currentStep,
          'border-gray-300': i + 1 >= currentStep
        }"
      ></div>
    </ng-container>
  </div>
</div>
