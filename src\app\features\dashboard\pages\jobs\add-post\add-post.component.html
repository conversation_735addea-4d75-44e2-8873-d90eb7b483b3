<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-4">Post a Job Opportunity</h1>
  <app-stepper [steps]="steps" [currentStep]="currentStep"></app-stepper>
  <div class="flex justify-end mt-8">
    <button
      *ngIf="currentStep > 1"
      class="bg-gray-300 text-gray-700 px-6 py-2.5 rounded-lg mr-4"
      (click)="currentStep = currentStep - 1"
    >
      Previous
    </button>
    <button
      *ngIf="currentStep < steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
      (click)="currentStep = currentStep + 1"
    >
      Next
    </button>
    <button
      *ngIf="currentStep === steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
    >
      Submit
    </button>
  </div>
  <div class="mt-8">
    <div *ngIf="currentStep === 1">
      <h2 class="text-xl font-semibold mb-4">Details</h2>
      <p>Here you can add the job title, location, and other details.</p>
    </div>
    <div *ngIf="currentStep === 2">
      <h2 class="text-xl font-semibold mb-4">Description</h2>
      <p>
        Provide a detailed description of the job responsibilities and
        requirements.
      </p>
    </div>
    <div *ngIf="currentStep === 3">
      <h2 class="text-xl font-semibold mb-4">Preview</h2>
      <p>Review the job posting before submitting.</p>
    </div>
  </div>
</div>
