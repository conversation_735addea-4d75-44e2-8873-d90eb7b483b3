import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard.component';
import { OverviewComponent } from './pages/overview/overview.component';
import { SettingsComponent } from './pages/settings/settings.component';
import { ProfileSettingsComponent } from './pages/settings/profile/profile-settings.component';
import { NotificationsSettingsComponent } from './pages/settings/notifications/notifications-settings.component';
import { BillingSettingsComponent } from './pages/settings/billing/billing-settings.component';
import { SecuritySettingsComponent } from './pages/settings/security/security-settings.component';
import { ProfileComponent } from './pages/profile/profile.component';
import { PrReleasesComponent } from './pages/pr-releases/pr-releases.component';
import { PrReleaseDetailComponent } from './pages/pr-releases/pr-release-detail/pr-release-detail.component';
import { JobsComponent } from './pages/jobs/jobs.component';
import { JobDetailComponent } from './pages/jobs/job-detail/job-detail.component';
import { AddPostComponent } from './pages/jobs/add-post/add-post.component';
import { EventsComponent } from './pages/events/events.component';
import { EventDetailComponent } from './pages/events/event-detail/event-detail.component';
import { MediaDataComponent } from './pages/media-data/media-data.component';
import { MyPitchesComponent } from './pages/my-pitches/my-pitches.component';
import { CompanyComponent } from './pages/settings/company/company.component';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    children: [
      {
        path: '',
        component: OverviewComponent,
      },
      {
        path: 'profile',
        component: ProfileComponent,
      },
      {
        path: 'settings',
        component: SettingsComponent,
        children: [
          {
            path: '',
            redirectTo: 'profile',
            pathMatch: 'full',
          },
          {
            path: 'profile',
            component: ProfileSettingsComponent,
          },
          {
            path: 'notifications',
            component: NotificationsSettingsComponent,
          },
          {
            path: 'billing',
            component: BillingSettingsComponent,
          },
          {
            path: 'security',
            component: SecuritySettingsComponent,
          },
          {
            path: 'company',
            component: CompanyComponent,
          },
        ],
      },
      {
        path: 'pr-releases',
        children: [
          {
            path: '',
            component: PrReleasesComponent,
          },
          {
            path: ':id',
            component: PrReleaseDetailComponent,
          },
        ],
      },
      {
        path: 'jobs',
        children: [
          {
            path: '',
            component: JobsComponent,
          },
          {
            path: 'add-post',
            component: AddPostComponent,
          },
          {
            path: ':id',
            component: JobDetailComponent,
          },
        ],
      },
      {
        path: 'events',
        loadChildren: () =>
          import('./pages/events/events.module').then((m) => m.EventsModule),
      },
      {
        path: 'media-database',
        component: MediaDataComponent,
      },
      {
        path: 'my-pitches',
        component: MyPitchesComponent,
      },
      {
        path: '',
        redirectTo: 'events',
        pathMatch: 'full',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}
